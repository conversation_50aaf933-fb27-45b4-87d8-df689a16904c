<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <meta name="description" content="About <PERSON> - Technical Combat Designer" />
  <title>About - <PERSON></title>

  <link rel="stylesheet" href="style.css" />
  <meta name="theme-color" content="#0f1724" />
</head>
<body>
  <a class="skip-link" href="#main">Skip to content</a>

  <header class="site-header">
    <div class="container header-inner">
      <div class="brand">
        <a href="index.html"><h1><PERSON></h1></a>
        <p class="sub">Technical / Combat Designer</p>
      </div>

      <button id="navToggle" class="nav-toggle" aria-expanded="false" aria-controls="siteNav" aria-label="Toggle navigation">
        <span></span><span></span><span></span>
      </button>

      <nav id="siteNav" class="site-nav" aria-label="Main navigation">
        <a href="index.html#projects">Projects</a>
        <a href="about.html" class="active">About</a>
        <a href="index.html#contact">Contact</a>
        <a href="documents/CV-Joseph Yan.pdf" target="_blank" class="cta">Resume</a>
      </nav>
    </div>
  </header>

  <main id="main">
    <section class="about-hero">
      <div class="container">
        <h1>About Me</h1>
        <p class="lead">Technical Combat Designer passionate about creating engaging gameplay experiences</p>
      </div>
    </section>

    <section class="about-content container">
      <div class="about-grid">
        <div class="about-text">
          <h2>Who I Am</h2>
          <p>I'm a technical and combat designer who loves prototyping combat systems, making VFXs, and creating gameplay modules. I work both in Unreal using Blueprint and Unity, bringing creative ideas to life through code and design.</p>
          
          <p>My passion lies in the intersection of technical implementation and creative design, where I can craft systems that feel both mechanically sound and emotionally engaging for players.</p>

          <h3>What I Do</h3>
          <ul class="skills-list">
            <li>Combat System Design & Implementation</li>
            <li>Technical Game Design</li>
            <li>VFX Creation & Integration</li>
            <li>Gameplay Module Development</li>
            <li>Unity & Unreal Engine Development</li>
            <li>Blueprint Visual Scripting</li>
          </ul>

          <h3>My Approach</h3>
          <p>I believe great games come from the perfect balance of technical excellence and creative vision. Whether I'm designing a complex parry system or creating satisfying visual feedback, I focus on how each element contributes to the overall player experience.</p>
        </div>

        <div class="about-sidebar">
          <div class="about-card">
            <h3>Location</h3>
            <p>Vancouver, Canada</p>
          </div>

          <div class="about-card">
            <h3>Specialties</h3>
            <ul>
              <li>Combat Design</li>
              <li>Technical Design</li>
              <li>VFX Creation</li>
              <li>System Implementation</li>
            </ul>
          </div>

          <div class="about-card">
            <h3>Tools & Engines</h3>
            <ul>
              <li>Unity</li>
              <li>Unreal Engine</li>
              <li>Blueprint</li>
              <li>C#</li>
              <li>Visual Effects</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <section class="about-cta container">
      <h2>Let's Work Together</h2>
      <p>I'm always interested in discussing new projects and opportunities in game development.</p>
      <div class="cta-buttons">
        <a href="index.html#projects" class="btn-primary">View My Work</a>
        <a href="index.html#contact" class="btn-secondary">Get In Touch</a>
      </div>
    </section>
  </main>

  <footer class="site-footer">
    <div class="container">
      <p>© <span id="year"></span> Joseph Yan — <a href="mailto:<EMAIL>">Email Me</a></p>
    </div>
  </footer>

  <script src="script.js" defer></script>
</body>
</html>
