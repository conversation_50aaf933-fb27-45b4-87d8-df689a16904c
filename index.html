<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <meta name="description" content="<PERSON>. Technical / Combat Designer." />
  <title><PERSON></title>

  <link rel="stylesheet" href="style.css" />
  <meta name="theme-color" content="#0f1724" />
</head>
<body>
  <a class="skip-link" href="#main">Skip to content</a>

  <header class="site-header">
    <div class="container header-inner">
      <div class="brand">
        <a href="#home"><h1><PERSON></h1></a>
        <p class="sub">Technical / Combat Designer</p>
      </div>

      <button id="navToggle" class="nav-toggle" aria-expanded="false" aria-controls="siteNav" aria-label="Toggle navigation">
        <span></span><span></span><span></span>
      </button>

      <nav id="siteNav" class="site-nav" aria-label="Main navigation">
        <a href="#projects">Projects</a>
        <a href="#contact">Contact</a>
        <a href="#contact" class="cta">Resume</a>
      </nav>
    </div>
  </header>

    <main id="main">
    <section id="home" class="hero-video">
    <video autoplay muted loop playsinline class="background-video">
        <source src="videos/portfolio-bg.mp4" type="video/mp4">
        Your browser does not support the video tag.
    </video>

    <div class="overlay">
        <h1 class="nameplate">Joseph Yan</h1>
        <p>Combat</p>
    </div>
    </section>
    </main>


    <section id="projects" class="projects container">
      <div class="section-head">
        <h3>Projects</h3>
        <p class="muted">Click a card to read more</p>
      </div>
      <div id="projectsGrid" class="grid"></div>
    </section>

    <section id="contact" class="contact container">
      <h3>Contact</h3>
      <p>I’m a technical and combat designer who loves prototyping combat systems, making VFXs, and making gameplay modules. I work both in Unreal using Blueprint and Unity.</p>
      <p>Feel free to contact or connect with me to talk about anything game related!</p>
      <p id="contact">Location: Vancouver, Canada • <a href="mailto:<EMAIL>"><EMAIL></a></p>
      <div class="socials">
        <!-- simple svg icons -->
        <a href="https://www.linkedin.com/in/joseph-yan-36a7bb175/" aria-label="LinkedIn" title="LinkedIn" class="icon-link">
          <svg viewBox="0 0 24 24" width="18" height="18" aria-hidden="true"><path d="M4.98 3.5C4.98 4.88 3.87 6 2.5 6S0 4.88 0 3.5 1.12 1 2.5 1 4.98 2.12 4.98 3.5zM0 8h5v16H0zM9 8h4.8v2.2h.1c.67-1.27 2.3-2.6 4.74-2.6C23 7.6 24 10 24 14v10h-5v-8.9c0-2.13-.04-4.87-2.96-4.87-2.96 0-3.41 2.31-3.41 4.7V30H9V8z" fill="currentColor"/></svg>
        </a>
      </div>
    </section>
  </main>

  <footer class="site-footer">
    <div class="container">
      <p>© <span id="year"></span> Joseph Yan — <a href="mailto:<EMAIL>">Email Me</a></p>
    </div>
  </footer>

  <!-- Project modal (hidden by default) -->
  <div id="projectModal" class="modal" role="dialog" aria-hidden="true" aria-labelledby="modalTitle">
    <div class="modal-dialog">
      <button class="modal-close" aria-label="Close project" id="modalClose">&times;</button>
      <div class="modal-body" id="modalContent" tabindex="0">
        <!-- Filled by script -->
      </div>
    </div>
  </div>

  <script src="script.js" defer></script>
</body>
</html>
