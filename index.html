<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <meta name="description" content="<PERSON>. Technical / Combat Designer." />
  <title><PERSON></title>

  <link rel="stylesheet" href="style.css" />
  <meta name="theme-color" content="#0f1724" />
</head>
<body>
  <a class="skip-link" href="#main">Skip to content</a>

  <header class="site-header">
    <div class="container header-inner">
      <div class="brand">
        <a href="#home"><h1><PERSON></h1></a>
        <p class="sub">Technical / Combat Designer</p>
      </div>

      <button id="navToggle" class="nav-toggle" aria-expanded="false" aria-controls="siteNav" aria-label="Toggle navigation">
        <span></span><span></span><span></span>
      </button>

      <nav id="siteNav" class="site-nav" aria-label="Main navigation">
        <a href="#projects">Projects</a>
        <a href="about.html">About</a>

        <a href="documents/CV-<PERSON>.pdf" target="_blank" class="cta">Resume</a>
      </nav>
    </div>
  </header>

    <main id="main">
    <section id="home" class="hero-video">
    <video autoplay muted loop playsinline class="background-video">
        <source src="videos/bg.mp4" type="video/mp4">
        Your browser does not support the video tag.
    </video>

    <div class="overlay">
        <h1 class="nameplate">Joseph Yan</h1>
        <p class="role">Technical Combat Designer</p>
    </div>
    </section>
    </main>


    <section id="projects" class="projects container">
      <div class="section-head">
        <h3>Projects</h3>
        <p class="muted">Click a card to read more</p>
      </div>
      <div id="projectsGrid" class="grid"></div>
    </section>


  </main>

  <footer class="site-footer">
    <div class="container">
      <p>© <span id="year"></span> Joseph Yan — <a href="mailto:<EMAIL>">Email Me</a></p>
    </div>
  </footer>

  <!-- Project modal (hidden by default) -->
  <div id="projectModal" class="modal" role="dialog" aria-hidden="true" aria-labelledby="modalTitle">
    <div class="modal-dialog">
      <button class="modal-close" aria-label="Close project" id="modalClose">&times;</button>
      <div class="modal-body" id="modalContent" tabindex="0">
        <!-- Filled by script -->
      </div>
    </div>
  </div>

  <script src="script.js" defer></script>
</body>
</html>
