:root{
  --bg:#0f1724;
  --card:#0b1220;
  --muted:#8b97a6;
  --accent:#5ed0ff;
  --surface:#0b1220;
  --text:#e6eef6;
  --gap:1.25rem;
  --maxw:1100px;
  --radius:12px;
  font-size:16px;
}

*{box-sizing:border-box}
html,body{height:100%}
body{
  margin:0;
  font-family: system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial;
  background:linear-gradient(180deg,#071020 0%, #0b1624 100%);
  color:var(--text);
  -webkit-font-smoothing:antialiased;
  -moz-osx-font-smoothing:grayscale;
  line-height:1.45;
}

.container{
  width:calc(100% - 2rem);
  max-width:var(--maxw);
  margin:0 auto;
}

a{color:var(--accent); text-decoration:none}
a:hover{text-decoration:underline}

.skip-link{
  position:absolute;
  left:-999px;
  top:auto;
  width:1px;height:1px;overflow:hidden;
}
.skip-link:focus{
  left:10px; top:10px; width:auto; height:auto; padding:8px 12px;
  background:#fff; color:#000; z-index:9999;
}

/* Header */
.site-header{
  padding:1rem 0;
  border-bottom:1px solid rgba(255,255,255,0.03);
}
.header-inner{display:flex;align-items:center;gap:1rem;justify-content:space-between}
.brand h1{margin:0;font-size:1.1rem;letter-spacing:0.6px}
.brand .sub{margin:0;color:var(--muted);font-size:0.9rem}

/* Nav */
.site-nav{
  display:flex;gap:1rem;align-items:center;
}
.site-nav a{padding:.35rem .6rem;border-radius:6px}
.site-nav .cta{background:rgba(255,255,255,0.03);padding:.4rem .7rem}

/* Mobile nav toggle */
.nav-toggle{display:none;background:none;border:0;padding:.35rem;cursor:pointer}
.nav-toggle span{display:block;width:20px;height:2px;background:var(--text);margin:4px 0;border-radius:2px}

/* Hero */
.hero{display:grid;grid-template-columns:1fr 380px;gap:2rem;align-items:center;padding:3rem 0}
.hero-left h2{font-size:1.8rem;margin:.2rem 0}
.lead{color:var(--muted);margin-top:.6rem}
.hero-right img{width:100%;border-radius:10px;display:block;box-shadow:0 8px 30px rgba(0,0,0,0.6)}

/* Hero section takes full viewport height */
.hero {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

/* Fullscreen background video */
.hero-video {
  position: relative;
  height: 80vh; /* reduced from full screen height */
  overflow: hidden;
}

.background-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.4;
}

.overlay {
  position: relative;
  z-index: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.nameplate {
  font-size: 4rem;
  font-weight: bold;
  color: white;
  text-align: center;
  letter-spacing: 2px;
  margin-bottom: 0.5rem;
}

.role {
  font-size: 1.5rem;
  color: white;
  text-align: center;
  font-weight: 300;
  letter-spacing: 1px;
  margin-top: 0;
}



/* Sections */
section{padding:2.2rem 0}
.section-head{display:flex;align-items:flex-end;justify-content:space-between}
.muted{color:var(--muted)}

/* Work list */
.work-list{list-style:none;padding:0;margin:1rem 0;display:grid;grid-template-columns:repeat(auto-fit,minmax(220px,1fr));gap:1rem}
.work-list li{background:rgba(255,255,255,0.02);padding:1rem;border-radius:10px}

/* Projects grid */
.grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(260px,1fr));gap:1.1rem;margin-top:1rem}
.card{
  background:linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0.01));
  border-radius:12px;overflow:hidden;cursor:pointer;
  transition:transform .18s ease,box-shadow .18s ease;
  display:flex;flex-direction:column;
}
.card:hover{transform:translateY(-6px);box-shadow:0 10px 30px rgba(0,0,0,0.6)}
.card img{width:100%;height:160px;object-fit:cover;display:block}
.card .card-body{padding:1rem}
.card h4{margin:0 0 .35rem}
.card p{margin:0;color:var(--muted);font-size:0.95rem}

/* Prototypes */
.proto-list{display:flex;gap:.6rem;flex-wrap:wrap}
.proto{background:rgba(255,255,255,0.03);padding:.6rem .9rem;border-radius:9px;font-size:.95rem}

/* About/socials */
.socials{margin-top:1rem;display:flex;gap:.5rem}
.icon-link{display:inline-flex;align-items:center;justify-content:center;padding:.4rem;border-radius:8px;background:rgba(255,255,255,0.02);min-width:40px}

/* Footer */
.site-footer{padding:1.2rem 0;border-top:1px solid rgba(255,255,255,0.03);color:var(--muted);text-align:center}

/* Modal */
.modal{position:fixed;inset:0;display:none;align-items:center;justify-content:center;padding:2rem;background:rgba(3,6,12,0.6);z-index:40}
.modal[aria-hidden="false"]{display:flex}
.modal-dialog{
  max-width:900px;
  width:100%;
  max-height:90vh;
  background:var(--surface);
  border-radius:12px;
  position:relative;
  display:flex;
  flex-direction:column;
}
.modal-close{position:absolute;right:.6rem;top:.4rem;background:none;border:0;color:var(--muted);font-size:1.8rem;cursor:pointer;z-index:10}
.modal-body{
  padding:1.2rem;
  padding-right:3rem;
  overflow-y:auto;
  flex:1;
  min-height:0;
}
.modal-body img{max-width:100%;border-radius:8px;margin-bottom:.6rem}

/* Project links */
.project-links{margin:1.5rem 0;text-align:center}
.itch-link{
  display:inline-block;
  background:linear-gradient(135deg, #fa5c5c, #e73c7e);
  color:white;
  text-decoration:none;
  padding:0.8rem 1.5rem;
  border-radius:8px;
  font-weight:500;
  transition:transform 0.2s ease, box-shadow 0.2s ease;
}
.itch-link:hover{
  transform:translateY(-2px);
  box-shadow:0 6px 20px rgba(231,60,126,0.4);
  text-decoration:none;
}

/* Project tabs */
.project-tabs{
  margin:2rem 0;
}
.tab-buttons{
  display:flex;
  gap:0.5rem;
  margin-bottom:1.5rem;
  border-bottom:1px solid rgba(255,255,255,0.1);
}
.tab-button{
  background:none;
  border:none;
  color:var(--muted);
  padding:0.8rem 1.2rem;
  font-size:1rem;
  cursor:pointer;
  border-radius:8px 8px 0 0;
  transition:all 0.2s ease;
  position:relative;
}
.tab-button:hover{
  color:white;
  background:rgba(255,255,255,0.05);
}
.tab-button.active{
  color:var(--accent);
  background:rgba(255,255,255,0.02);
}
.tab-button.active::after{
  content:'';
  position:absolute;
  bottom:-1px;
  left:0;
  right:0;
  height:2px;
  background:var(--accent);
}
.tab-content{
  position:relative;
}
.tab-panel{
  display:none;
}
.tab-panel.active{
  display:block;
}
.tab-panel > p{
  margin:0 0 1.5rem 0;
  color:var(--muted);
}

/* Video grid */
.video-grid{
  display:grid;
  grid-template-columns:repeat(auto-fit, minmax(300px, 1fr));
  gap:1.5rem;
}
.video-item{
  background:rgba(255,255,255,0.02);
  border-radius:12px;
  overflow:hidden;
  transition:transform 0.2s ease;
}
.video-item:hover{
  transform:translateY(-2px);
}
.video-item video{
  width:100%;
  height:auto;
  display:block;
}
.video-info{
  padding:1rem;
}
.video-info h4{
  margin:0 0 0.5rem 0;
  font-size:1.1rem;
  color:white;
}
.video-info p{
  margin:0;
  color:var(--muted);
  font-size:0.9rem;
  line-height:1.4;
}

/* Responsive */
@media (max-width:880px){
  .hero{grid-template-columns:1fr; padding:2rem 0}
  .hero-right{order:-1}
  .nav-toggle{display:inline-block}
  .site-nav{position:absolute;right:12px;top:72px;background:rgba(7,10,15,0.95);padding:.8rem;border-radius:10px;flex-direction:column;display:none}
  .site-nav a{display:block}
  .site-nav.open{display:flex}

  /* Modal responsive */
  .modal{padding:1rem}
  .modal-dialog{max-height:95vh}
  .modal-body{padding:1rem}
  .video-grid{grid-template-columns:1fr}
  .tab-buttons{flex-wrap:wrap}
}
